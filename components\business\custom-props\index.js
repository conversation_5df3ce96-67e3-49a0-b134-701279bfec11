import dynamic from "next/dynamic";
import styles from "./index.module.scss";
import { useEffect, useRef, useState } from "react";
import Helper from "@/helper/helper";
import { useRouter } from "next/router";
import Enums from "enums";
import store from "store";
import { FormControl, ControlWrapper } from "@/components/common/react-form-x";
import { CustomPropsHelper } from "./common/vars";
import FormItem from "../form-item";
import classNames from "classnames";

const dynamicOptions = { ssr: false };

const Controls = {
  [Enums.ControlType.Select]: dynamic(() => import("./controls/select"), dynamicOptions),
  [Enums.ControlType.Text]: dynamic(() => import("./controls/text"), dynamicOptions),
  [Enums.ControlType.Textarea]: dynamic(() => import("./controls/text"), dynamicOptions),
  [Enums.ControlType.Swatch]: dynamic(() => import("./controls/swatch"), dynamicOptions),
  [Enums.ControlType.Group]: dynamic(() => import("./controls/group"), dynamicOptions),
  [Enums.ControlType.SelectIcon]: dynamic(() => import("./controls/select-icon"), dynamicOptions),
  [Enums.ControlType.Stepper]: dynamic(() => import("./controls/stepper"), dynamicOptions),
  [Enums.ControlType.Popup]: dynamic(() => import("./controls/popup"), dynamicOptions),
  [Enums.ControlType.Upload]: dynamic(() => import("./controls/upload"), dynamicOptions),
  [Enums.ControlType.TextGroup]: dynamic(() => import("./controls/text-group"), dynamicOptions),
  [Enums.ControlType.Spotify]: dynamic(() => import("./controls/spotify"), dynamicOptions),
  [Enums.ControlType.PreviewText]: dynamic(() => import("./controls/preview-text"), dynamicOptions),
  [Enums.ControlType.CompositeGroup]: dynamic(() => import("./controls/composite-group"), dynamicOptions),
  [Enums.ControlType.DatePicker]: dynamic(() => import("./controls/date-picker"), dynamicOptions),
  [Enums.ControlType.RadioList]: dynamic(() => import("./controls/radio-list"), dynamicOptions),
  [Enums.ControlType.GlassesSelect]: dynamic(() => import("./controls/glasses-select"), dynamicOptions),
  [Enums.ControlType.ProductSuite]: dynamic(() => import("./controls/product-suite"), dynamicOptions),
  [Enums.ControlType.SsySelect]: dynamic(() => import("./controls/ssy-select"), dynamicOptions),
  [Enums.ControlType.UpgradeMoissanite]: dynamic(() => import("./controls/upgrade-moissanite"), dynamicOptions),
  [Enums.ControlType.DialogCheckbox]: dynamic(() => import("./controls/dialog-checkbox"), dynamicOptions),
  [Enums.ControlType.StarMap]: dynamic(() => import("./controls/star-map"), dynamicOptions),
  [Enums.ControlType.Map]: dynamic(() => import("./controls/map"), dynamicOptions),
  [Enums.ControlType.Crossword]: dynamic(() => import("./controls/crossword"), dynamicOptions),
  [Enums.ControlType.Pod]: dynamic(() => import(`./controls/pod`), dynamicOptions),
  [Enums.ControlType.MoonPhase]: dynamic(() => import("./controls/moon-phase"), dynamicOptions),
  [Enums.ControlType.YesNoToggle]: dynamic(() => import("./controls/yes-no-toggle"), dynamicOptions),
  [Enums.ControlType.MultiStyleImage]: dynamic(() => import("./controls/multi-style-image"), dynamicOptions),
};

const Widgets = {
  [Enums.WidgetType.SizeGuide]: dynamic(() => import(`./widgets/size-guide`), dynamicOptions),
};

const CustomProps = (props) => {
  const { data, defaultValues, rules, product, tryAtHome, price, keyPath = [], updatePrice, onValueChange } = props;
  const router = useRouter();
  const { config } = store;
  const [labels, setLabels] = useState({});
  const paramsRef = useRef({});
  paramsRef.current = { ...paramsRef.current, defaultValues, keyPath };

  function setSpecialLabels(item, option) {
    const optionLabel = CustomPropsHelper.getCustomPropsOptionLabel(option);
    const itemKey = item.key + (item.keyPath || []).join("-");
    setLabels((labels) => ({
      ...labels,
      [itemKey]: `${item.title}: ${optionLabel}`,
    }));
  }

  function hasWidget(item) {
    return (
      item?.explanation_block_identifier ||
      (product?.size_guide_block_identifier && item?.key === Enums.CustomPropsType.Secondary)
    );
  }

  function getPropsFromControl(type, controlProps) {
    if ([Enums.ControlType.Upload, Enums.ControlType.MultiStyleImage].includes(type)) {
      return {
        setErrorMessage: controlProps.setErrorMessage,
      };
    }
  }

  function parseValueForLabel(item, value) {
    if (item.type === Enums.ControlType.MultiStyleImage) {
      return value.style;
    }
    return value;
  }

  useEffect(() => {
    const { keyPath } = paramsRef.current;
    if (Array.isArray(data)) {
      for (const item of data) {
        // Swatch 和 RadioList 默认 label
        if (item.change_title) {
          const defaultValue = CustomPropsHelper.getValueByPath(
            paramsRef.current.defaultValues,
            keyPath.concat(item.key).concat(item.keyPath || [])
          );
          if (defaultValue) {
            const option = item.options?.find((a) => a.value === parseValueForLabel(item, defaultValue));
            setSpecialLabels(item, option);
          }
        }
      }
    }
  }, [data, router.asPath]);

  return (
    data?.map?.((item) => {
      const fieldName = item?.key;
      if (!fieldName) {
        throw new Error(`[Custom Props]: key is required!`);
      }

      const Control = Controls[item.type] || null;
      const Widget = Widgets[Enums.WidgetType.SizeGuide];
      const rule = rules?.[fieldName];

      const fullKeyPath = keyPath.concat(item.keyPath || []);
      const defaultValue = CustomPropsHelper.getValueByPath(defaultValues, fullKeyPath.concat(fieldName));
      const formControlProps = {
        rule,
        keyPath: fullKeyPath,
        name: fieldName,
      };
      const controlProps = {
        price,
        product,
        data: item,
        keyPath: fullKeyPath,
      };
      const itemKey = item.key + (item.keyPath || []).join("-");
      const formItemProps = {
        required: rule?.required ?? false,
        label: labels[itemKey] ?? item.title,
      };

      const hasSizeGuide = hasWidget(item);

      const isWrapper = [
        Enums.ControlType.PreviewText,
        Enums.ControlType.Stepper,
        Enums.ControlType.Group,
        Enums.ControlType.TextGroup,
      ].includes(item.type);

      if (item.placeholder) {
        controlProps.placeholder = item.placeholder;
      }

      // 色卡等组件选中值改变需要改变title
      if (item.change_title) {
        controlProps.onLabelChange = (e) => {
          const option = item.options?.find((item) => item.value === e.target.value);
          setSpecialLabels(item, option);
        };
      }

      if (item.type === Enums.ControlType.Group) {
        const groupItemsKey = Helper.getGroupItemsKey(fieldName);
        const defaultGroupItemsValue = defaultValues?.[groupItemsKey];
        if (defaultGroupItemsValue) {
          controlProps.defaultGroupItemsValue = { [groupItemsKey]: defaultGroupItemsValue };
        }
        // 固定分组不显示title
        if (item.style === "fixed") {
          item.title = "";
          item.required = false;
        }
      }

      // aoolia站frame width
      if (
        config?.page_template === Enums.PageTemplate.Aoolia &&
        item.type === Enums.ControlType.RadioList &&
        hasSizeGuide
      ) {
        const frameWidthItem = product?.specification
          ?.find((record) => record?.key === "frame_dimension")
          ?.children?.find((item) => item?.key === "frame_width");
        controlProps.frameWidth = frameWidthItem ? (
          <div className={styles.frameWidth}>{`${frameWidthItem?.label || ""}: ${frameWidthItem?.value || ""}`}</div>
        ) : null;
      }

      // popup必填校验
      if (item.type === Enums.ControlType.Popup && item.children?.some((a) => a.required)) {
        item.required = true;
      }

      // 刻字组展开时不显示title
      if (item.type === Enums.ControlType.TextGroup && item.style === "fixed") {
        item.title = "";
      }

      // 升级莫桑组件传递updatePrice
      if (item.type === Enums.ControlType.UpgradeMoissanite || item.type === Enums.ControlType.GlassesSelect) {
        controlProps.updatePrice = updatePrice;
      }

      // ssy_select组件传递tryAtHome
      if (item.type === Enums.ControlType.SsySelect) {
        controlProps.tryAtHome = tryAtHome;
      }

      // yes-no-toggle 不展示form label
      if (item.type === Enums.ControlType.YesNoToggle) {
        formItemProps.showLabel = false;
      }

      return (
        <div
          key={formControlProps.keyPath.join("") + fieldName}
          className={classNames(styles.customPropsItem, { [styles.disabled]: item?.disabled })}
          data-custom-props-component={item.type}
        >
          {isWrapper ? (
            Control ? (
              <Control {...controlProps} {...formControlProps} defaultValue={defaultValue} />
            ) : null
          ) : (
            <FormControl
              {...formControlProps}
              render={(props, extra) => (
                <ControlWrapper
                  {...props}
                  defaultValue={defaultValue}
                  render={(props) => (
                    <FormItem {...formItemProps}>
                      {Control ? (
                        <Control
                          {...controlProps}
                          {...props}
                          {...getPropsFromControl(item.type, extra)}
                          defaultValue={defaultValue}
                          onChange={(e, value) => {
                            props.onChange(e, value);
                            onValueChange?.({
                              keyPath,
                              name: fieldName,
                              value: value || e?.target?.value,
                            });
                          }}
                        />
                      ) : null}
                    </FormItem>
                  )}
                />
              )}
            />
          )}

          {hasSizeGuide ? (
            <div className={styles.widget}>
              <Widget
                data={item}
                product={product}
                blockId={item?.explanation_block_identifier || product.size_guide_block_identifier}
              />
            </div>
          ) : null}
        </div>
      );
    }) || <div />
  );
};

export default CustomProps;
