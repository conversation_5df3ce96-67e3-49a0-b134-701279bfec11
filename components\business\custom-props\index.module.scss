@import "styles/variables";

.customPropsItem {
  position: relative;

  &:has([class*=""])

  .widget {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    cursor: pointer;
  }

  .frameWidth {
    display: inline-block;
    padding: 0 10px;
    font-weight: 700;
  }

  &[data-custom-props-component="crossword"] {
    [class*="has-error"] {
      [class*="form-item_formItem"] {
        margin-bottom: 40px;
      }
    }
  }
}

.disabled {
  cursor: not-allowed;
  opacity: 0.6;

  * {
    pointer-events: none;
  }
}
