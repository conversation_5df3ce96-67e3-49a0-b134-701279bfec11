import { useState } from "react";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";
import dynamic from "next/dynamic";
import SvgIcon from "components/common/svg-icon";

import Loading from "@/components/common/loading";
import store from "store";
import Fetchers from "@/fetchers";
import Enums from "enums";

import Dialog from "./components/dialog";

import styles from "./size-guide.module.scss";

const TEMPLATE_MAP = {
  [Enums.PageTemplate.Aoolia]: dynamic(() => import("./components/eyeglasses-size-guide"), { ssr: false }),
  [Enums.PageTemplate.Huglittle]: dynamic(() => import("./components/huglittle-size-guide"), { ssr: false }),
  [Enums.PageTemplate.Fanscheer]: dynamic(() => import("./components/fanscheer-size-guide"), { ssr: false }),
};

const HtmlBlock = dynamic(() => import("@/components/business/html-block"), { ssr: false });

const Cache = {};
function SizeGuide(props) {
  const { blockId, product, data } = props;
  const { t } = useTranslation();
  const { isMobile, config } = store;
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogContent, setDialogContent] = useState(null);
  const [dialogLoading, setDialogLoading] = useState(true);
  const triggerLabel = data?.explanation_block_title || t("SizeGuide");
  const dialogTitle = data?.explanation_block_title || dialogContent?.title;

  function openDialog() {
    setDialogOpen(true);
  }

  function closeDialog() {
    setDialogOpen(false);
  }

  async function handleBeforeDialogOpen() {
    try {
      setDialogLoading(true);
      let content = Cache[blockId];
      if (!content) {
        content = await Fetchers.getCmsBlock({ id: blockId }).then((res) => res.data?.data?.[blockId]);
        Cache[blockId] = content;
      }
      setDialogContent(content);
      setDialogLoading(false);
    } catch (e) {
      setDialogLoading(false);
    }
  }

  const Template = TEMPLATE_MAP[config?.page_template];

  return (
    <div className={styles.sizeGuide}>
      <span className={styles.trigger} onClick={openDialog}>
        <SvgIcon viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z" />
        </SvgIcon>
        {triggerLabel}
      </span>
      <Dialog
        isMobile={isMobile}
        open={dialogOpen}
        width={dialogContent?.width}
        title={dialogTitle}
        onClose={closeDialog}
        onBeforeOpen={handleBeforeDialogOpen}
      >
        <Loading loading={dialogLoading}>
          {Template ? <Template product={product} content={dialogContent} /> : <HtmlBlock html={dialogContent} />}
        </Loading>
      </Dialog>
    </div>
  );
}

SizeGuide.propTypes = {
  blockId: PropTypes.string,
  product: PropTypes.object,
  data: PropTypes.object,
};

export default SizeGuide;
